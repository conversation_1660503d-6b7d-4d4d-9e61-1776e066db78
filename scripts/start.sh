#!/bin/bash

# Start script for Payroll MCP Server

set -e

echo "Starting Payroll MCP Server..."

# Check if .env file exists
if [ ! -f .env ]; then
    echo "Warning: .env file not found. Using environment variables or defaults."
    echo "Copy .env.example to .env and configure your settings."
fi

# Check if dist directory exists
if [ ! -d "dist" ]; then
    echo "Building project..."
    npm run build
fi

# Start the server
echo "Starting server..."
npm start
