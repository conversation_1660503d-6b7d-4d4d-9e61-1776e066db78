# API Usage Guide

This document provides examples of how to use the Payroll MCP Server API.

## Authentication

All API calls (except health check) require authentication using OIDC Bearer tokens.

### 1. Get Access Token

#### Option A: Browser Flow
```bash
# Redirect user to login
curl "http://localhost:3000/auth/login"

# User completes login and is redirected to callback
# Extract access_token from callback response
```

#### Option B: Direct Token Exchange (if you have authorization code)
```bash
curl -X GET "http://localhost:3000/auth/callback?code=YOUR_AUTH_CODE&state=YOUR_STATE"
```

### 2. Use Access Token

Include the token in the Authorization header:
```bash
curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     http://localhost:3000/mcp/tools
```

## API Endpoints

### Health Check
```bash
curl http://localhost:3000/health
```

Response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0"
}
```

### List Available Tools
```bash
curl -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
     http://localhost:3000/mcp/tools
```

### Execute Payroll Tool

#### Basic Request
```bash
curl -X POST http://localhost:3000/mcp/tools/payroll \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "owners",
    "farming_year": 16
  }'
```

#### Advanced Request with Filters
```bash
curl -X POST http://localhost:3000/mcp/tools/payroll \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "owners",
    "farming_year": 16,
    "payroll_from_date": "2024-10-01",
    "payroll_to_date": "2025-09-30",
    "owner_type": "1",
    "owner_names": "иван",
    "page": 1,
    "rows": 50,
    "sort": "owner_names",
    "order": "asc"
  }'
```

#### Request with Multiple Filters
```bash
curl -X POST http://localhost:3000/mcp/tools/payroll \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "owners",
    "farming_year": 16,
    "payroll_ekate": ["123", "456"],
    "payroll_farming": [1, 2, 3],
    "owner_egns": ["1234567890", "0987654321"],
    "rep_names": "петър",
    "rent_place": "София"
  }'
```

## Parameter Reference

### Required Parameters
- `type`: `"owners"` | `"sums"` | `"payroll_by_owner"`
- `farming_year`: integer (farming year ID)

### Optional Filters

#### Date Range
- `payroll_from_date`: string (YYYY-MM-DD)
- `payroll_to_date`: string (YYYY-MM-DD)

#### Location Filters
- `payroll_ekate`: array of strings (EKATE codes)
- `payroll_farming`: array of strings/integers (farming IDs)
- `rent_place`: string (owner location)
- `rep_rent_place`: string (representative location)

#### Owner Filters
- `owner_type`: `"0"` (companies) | `"1"` (individuals) | `"0,1"` (both)
- `owner_names`: string (name search for individuals)
- `egn`: string (individual ID number)
- `eik`: string (company ID number)
- `company_name`: string (company name search)
- `owner_egns`: array of strings (multiple individual IDs)
- `company_eiks`: array of strings (multiple company IDs)

#### Representative Filters
- `rep_names`: string (representative name search)
- `rep_egn`: string (representative ID number)

#### Heritor Filters
- `heritor_names`: string (heritor name search)
- `heritor_egn`: string (heritor ID number)

#### Pagination & Sorting
- `page`: integer (default: 1)
- `rows`: integer (default: 30, max: 1000)
- `sort`: string (default: "owner_names")
- `order`: `"asc"` | `"desc"` (default: "asc")

## Response Format

### Success Response
```json
{
  "result": {
    "rows": [
      {
        "owner_id": 123,
        "owner_names": "Иван Петров",
        "egn_eik": "1234567890",
        "area": "10.500",
        "renta": "1500.00",
        "paid_renta": "1200.00",
        "unpaid_renta": "300.00",
        ...
      }
    ],
    "total": 150,
    "footer": [
      {
        "renta": "45000.00",
        "paid_renta": "38000.00",
        "unpaid_renta": "7000.00",
        ...
      }
    ]
  }
}
```

### Error Response
```json
{
  "error": "TOOL_EXECUTION_ERROR",
  "message": "Invalid parameters: type is required"
}
```

## MCP Streaming Endpoint

For MCP clients that support streaming:

```bash
curl -X POST http://localhost:3000/mcp/stream \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "method": "tools/call",
    "params": {
      "name": "payroll",
      "arguments": {
        "type": "owners",
        "farming_year": 16
      }
    }
  }'
```

## Error Codes

- `400` - Bad Request (invalid parameters)
- `401` - Unauthorized (missing or invalid token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (tool not found)
- `500` - Internal Server Error

## Rate Limiting

Default limits:
- 100 requests per 15 minutes per IP
- Configurable via environment variables

## Examples by Use Case

### Get All Owners for a Farming Year
```json
{
  "type": "owners",
  "farming_year": 16,
  "page": 1,
  "rows": 100
}
```

### Search for Specific Individual
```json
{
  "type": "owners",
  "farming_year": 16,
  "owner_type": "1",
  "egn": "1234567890"
}
```

### Get Company Payroll Data
```json
{
  "type": "owners",
  "farming_year": 16,
  "owner_type": "0",
  "company_name": "АГРО"
}
```

### Get Summary Data
```json
{
  "type": "sums",
  "farming_year": 16,
  "payroll_from_date": "2024-01-01",
  "payroll_to_date": "2024-12-31"
}
```

### Search by Location
```json
{
  "type": "owners",
  "farming_year": 16,
  "payroll_ekate": ["12345"],
  "rent_place": "София"
}
```
