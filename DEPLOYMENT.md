# Deployment Guide

This guide covers deploying the Payroll MCP Server in various environments.

## Prerequisites

- Node.js 18+ (for local development)
- <PERSON><PERSON> and <PERSON><PERSON> Compose (for containerized deployment)
- Keycloak server for OIDC authentication
- Access to external payroll JSON-RPC API

## Environment Configuration

### Required Environment Variables

```bash
# OIDC Configuration
OIDC_ISSUER_URL=https://your-keycloak-server/realms/your-realm
OIDC_CLIENT_ID=payroll-mcp-client
OIDC_CLIENT_SECRET=your-client-secret
OIDC_REDIRECT_URI=http://localhost:3000/auth/callback

# External API Configuration
EXTERNAL_API_URL=https://your-payroll-api.com

# Security Configuration
JWT_SECRET=your-jwt-secret-key
SESSION_SECRET=your-session-secret-key
```

### Optional Environment Variables

```bash
# Server Configuration
PORT=3000
NODE_ENV=production

# OIDC Configuration
OIDC_SCOPE=openid profile email

# External API Configuration
EXTERNAL_API_TIMEOUT=30000

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# CORS Configuration
CORS_ORIGIN=*
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

## Local Development

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

3. **Build and start:**
   ```bash
   npm run build
   npm start
   ```

4. **Development mode with hot reload:**
   ```bash
   npm run dev
   ```

## Docker Deployment

### Using Docker Compose (Recommended)

1. **Configure environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

2. **Start services:**
   ```bash
   docker-compose up -d
   ```

3. **View logs:**
   ```bash
   docker-compose logs -f payroll-mcp-server
   ```

4. **Stop services:**
   ```bash
   docker-compose down
   ```

### Using Docker directly

1. **Build image:**
   ```bash
   docker build -t payroll-mcp-server:latest .
   ```

2. **Run container:**
   ```bash
   docker run -d \
     --name payroll-mcp-server \
     -p 3000:3000 \
     --env-file .env \
     payroll-mcp-server:latest
   ```

## Production Deployment

### With Reverse Proxy (Traefik)

1. **Start with proxy profile:**
   ```bash
   docker-compose --profile proxy up -d
   ```

2. **Configure DNS:**
   - Point your domain to the server
   - Update Traefik labels in docker-compose.yml

### Security Considerations

1. **Use HTTPS in production:**
   - Configure SSL certificates
   - Update OIDC_REDIRECT_URI to use HTTPS

2. **Secure environment variables:**
   - Use Docker secrets or external secret management
   - Never commit .env files to version control

3. **Network security:**
   - Use private networks for internal communication
   - Implement proper firewall rules

4. **Monitoring:**
   - Set up log aggregation
   - Configure health check monitoring
   - Set up alerts for failures

## Keycloak Configuration

### Create OIDC Client

1. **Login to Keycloak Admin Console**

2. **Create new client:**
   - Client ID: `payroll-mcp-client`
   - Client Protocol: `openid-connect`
   - Access Type: `confidential`

3. **Configure client:**
   - Valid Redirect URIs: `http://your-domain/auth/callback`
   - Web Origins: `http://your-domain`

4. **Get client secret:**
   - Go to Credentials tab
   - Copy the secret for OIDC_CLIENT_SECRET

### Configure Scopes and Roles

1. **Create roles:**
   - `payroll-user`: Basic access to payroll data
   - `payroll-admin`: Administrative access

2. **Assign roles to users**

3. **Configure client scopes if needed**

## API Integration

### External Payroll API

The server expects a JSON-RPC API at:
```
POST {EXTERNAL_API_URL}/index.php?payroll-rpc=payroll-grid
```

Request format:
```json
{
  "method": "read",
  "params": [
    {
      "type": "owners",
      "farming_year": 16,
      ...
    },
    1,
    30,
    "owner_names",
    "asc"
  ],
  "id": 1,
  "jsonrpc": "2.0"
}
```

## Health Checks

The server provides a health check endpoint:
```bash
curl http://localhost:3000/health
```

Response:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0"
}
```

## Troubleshooting

### Common Issues

1. **OIDC Connection Failed:**
   - Verify OIDC_ISSUER_URL is accessible
   - Check Keycloak client configuration
   - Verify network connectivity

2. **External API Errors:**
   - Check EXTERNAL_API_URL configuration
   - Verify API endpoint is accessible
   - Check authentication token validity

3. **Docker Issues:**
   - Ensure Docker daemon is running
   - Check port conflicts
   - Verify environment variables are set

### Logs

View application logs:
```bash
# Docker Compose
docker-compose logs -f payroll-mcp-server

# Docker
docker logs -f payroll-mcp-server

# Local
npm start
```

### Debug Mode

Enable debug logging:
```bash
LOG_LEVEL=debug npm start
```

## Scaling

### Horizontal Scaling

1. **Load Balancer:**
   - Use nginx or Traefik
   - Configure session affinity if needed

2. **Multiple Instances:**
   ```bash
   docker-compose up --scale payroll-mcp-server=3
   ```

### Performance Tuning

1. **Node.js Options:**
   ```bash
   NODE_OPTIONS="--max-old-space-size=2048" npm start
   ```

2. **Rate Limiting:**
   - Adjust RATE_LIMIT_* variables
   - Consider Redis for distributed rate limiting

3. **Caching:**
   - Implement Redis caching for API responses
   - Cache OIDC user info

## Backup and Recovery

### Configuration Backup

- Backup .env files securely
- Document Keycloak client configuration
- Backup any custom certificates

### Data Recovery

- The server is stateless
- Recovery involves redeploying with correct configuration
- Ensure external API and OIDC provider are available
