#!/usr/bin/env node

// Simple test to verify configuration loading
// This doesn't require external services

const path = require('path');

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.PORT = '3001';
process.env.OIDC_ISSUER_URL = 'https://test-keycloak.example.com/realms/test';
process.env.OIDC_CLIENT_ID = 'test-client';
process.env.OIDC_CLIENT_SECRET = 'test-secret';
process.env.OIDC_REDIRECT_URI = 'http://localhost:3001/auth/callback';
process.env.EXTERNAL_API_URL = 'https://test-api.example.com';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.SESSION_SECRET = 'test-session-secret';

console.log('Testing configuration loading...');

try {
  // Test configuration loading
  const { config } = require('./dist/utils/config');
  
  console.log('✓ Configuration loaded successfully');
  console.log('  - Server port:', config.server.port);
  console.log('  - Node environment:', config.server.nodeEnv);
  console.log('  - OIDC issuer:', config.oidc.issuerUrl);
  console.log('  - External API URL:', config.externalApi.url);
  
  // Test logger
  const { logger } = require('./dist/utils/logger');
  logger.info('Logger test message');
  console.log('✓ Logger initialized successfully');
  
  console.log('\n✅ All configuration tests passed!');
  console.log('\nTo start the server:');
  console.log('  1. Copy .env.example to .env');
  console.log('  2. Configure your OIDC and API settings');
  console.log('  3. Run: npm start');
  
} catch (error) {
  console.error('❌ Configuration test failed:', error.message);
  process.exit(1);
}
