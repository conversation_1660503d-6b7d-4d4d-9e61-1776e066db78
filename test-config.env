# Test configuration for Payroll MCP Server
# This file contains test values - DO NOT use in production

# Server Configuration
PORT=3001
NODE_ENV=test

# OIDC Configuration (test values)
OIDC_ISSUER_URL=https://test-keycloak.example.com/realms/test
OIDC_CLIENT_ID=test-client
OIDC_CLIENT_SECRET=test-secret
OIDC_REDIRECT_URI=http://localhost:3001/auth/callback
OIDC_SCOPE=openid profile email

# External API Configuration (test values)
EXTERNAL_API_URL=https://test-api.example.com
EXTERNAL_API_TIMEOUT=5000

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=simple

# Security Configuration (test values)
JWT_SECRET=test-jwt-secret-key-for-testing-only
SESSION_SECRET=test-session-secret-key-for-testing-only

# CORS Configuration
CORS_ORIGIN=http://localhost:3001
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=1000
