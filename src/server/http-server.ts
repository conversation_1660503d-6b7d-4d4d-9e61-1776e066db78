import express, { Express, Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { config } from '../utils/config';
import { logger, requestLogger } from '../utils/logger';
import { oidcClient } from '../auth/oidc-client';
import { requireAuth, optionalAuth } from '../middleware/auth';
import { payrollTool, handlePayrollTool } from '../mcp/payroll-tool';
import { CallToolRequest } from '@modelcontextprotocol/sdk/types.js';

/**
 * HTTP Server with Streamable HTTP transport for MCP
 */
export class HttpServer {
  private app: Express;
  private server: any;

  constructor() {
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  /**
   * Setup Express middleware
   */
  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet());
    
    // CORS middleware
    this.app.use(cors({
      origin: config.server.corsOrigin,
      credentials: config.server.corsCredentials
    }));

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // Request logging
    this.app.use(requestLogger);
  }

  /**
   * Setup Express routes
   */
  private setupRoutes(): void {
    // Health check endpoint
    this.app.get('/health', (_req: Request, res: Response) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      });
    });

    // OIDC authentication routes
    this.app.get('/auth/login', (req: Request, res: Response) => {
      try {
        const state = req.query['state'] as string;
        const authUrl = oidcClient.getAuthorizationUrl(state);
        res.redirect(authUrl);
      } catch (error) {
        logger.error('Login redirect failed', { error });
        res.status(500).json({ error: 'Authentication service unavailable' });
      }
    });

    this.app.get('/auth/callback', async (req: Request, res: Response): Promise<void> => {
      try {
        const { code, state } = req.query;

        if (!code) {
          res.status(400).json({ error: 'Authorization code missing' });
          return;
        }

        const tokenSet = await oidcClient.exchangeCodeForTokens(
          code as string,
          state as string
        );

        // In a real implementation, you'd store the token securely
        // and return it to the client or redirect with success
        res.json({
          message: 'Authentication successful',
          access_token: tokenSet.access_token,
          expires_at: tokenSet.expires_at
        });

      } catch (error) {
        logger.error('Authentication callback failed', { error });
        res.status(400).json({ error: 'Authentication failed' });
      }
    });

    // MCP Tools endpoint - List available tools
    this.app.get('/mcp/tools', optionalAuth, (_req: Request, res: Response) => {
      res.json({
        tools: [payrollTool]
      });
    });

    // MCP Tools endpoint - Execute tool
    this.app.post('/mcp/tools/:toolName', requireAuth, async (req: Request, res: Response): Promise<void> => {
      try {
        const { toolName } = req.params;
        const { arguments: toolArgs } = req.body;

        if (toolName !== 'payroll') {
          res.status(404).json({
            error: 'TOOL_NOT_FOUND',
            message: `Tool '${toolName}' not found`
          });
          return;
        }

        // Create MCP tool request
        const toolRequest: CallToolRequest = {
          method: 'tools/call',
          params: {
            name: toolName,
            arguments: toolArgs
          }
        };

        // Execute tool with user's access token
        const result = await handlePayrollTool(
          toolRequest,
          req.user!.tokenSet.access_token
        );

        if (result.isError) {
          res.status(400).json({
            error: 'TOOL_EXECUTION_ERROR',
            message: result.content[0]?.text || 'Tool execution failed'
          });
          return;
        }

        const resultText = result.content[0]?.text;
        res.json({
          result: resultText && typeof resultText === 'string' && resultText.trim()
            ? JSON.parse(resultText)
            : null
        });

      } catch (error) {
        logger.error('Tool execution failed', { error, toolName: req.params['toolName'] });
        res.status(500).json({
          error: 'INTERNAL_ERROR',
          message: 'Tool execution failed'
        });
      }
    });

    // MCP Streamable HTTP transport endpoint
    this.app.post('/mcp/stream', requireAuth, async (req: Request, res: Response) => {
      try {
        // Set headers for streaming response
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Transfer-Encoding', 'chunked');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');

        const { method, params } = req.body;

        switch (method) {
          case 'tools/list':
            res.write(JSON.stringify({
              jsonrpc: '2.0',
              id: params?.id || 1,
              result: { tools: [payrollTool] }
            }));
            break;

          case 'tools/call':
            const toolRequest: CallToolRequest = {
              method: 'tools/call',
              params: params
            };

            const result = await handlePayrollTool(
              toolRequest,
              req.user!.tokenSet.access_token
            );

            res.write(JSON.stringify({
              jsonrpc: '2.0',
              id: params?.id || 1,
              result: result
            }));
            break;

          default:
            res.write(JSON.stringify({
              jsonrpc: '2.0',
              id: params?.id || 1,
              error: {
                code: -32601,
                message: 'Method not found'
              }
            }));
        }

        res.end();

      } catch (error) {
        logger.error('Streaming request failed', { error });
        res.status(500).json({
          jsonrpc: '2.0',
          id: req.body.params?.id || 1,
          error: {
            code: -32603,
            message: 'Internal error'
          }
        });
      }
    });

    // 404 handler
    this.app.use('*', (_req: Request, res: Response) => {
      res.status(404).json({
        error: 'NOT_FOUND',
        message: 'Endpoint not found'
      });
    });

    // Error handler
    this.app.use((error: any, req: Request, res: Response, _next: any) => {
      logger.error('Unhandled error', { error, url: req.url, method: req.method });
      res.status(500).json({
        error: 'INTERNAL_ERROR',
        message: 'Internal server error'
      });
    });
  }

  /**
   * Start the HTTP server
   */
  async start(): Promise<void> {
    try {
      // Initialize OIDC client
      await oidcClient.initialize();

      // Start HTTP server
      this.server = this.app.listen(config.server.port, () => {
        logger.info('HTTP Server started', {
          port: config.server.port,
          nodeEnv: config.server.nodeEnv
        });
      });

    } catch (error) {
      logger.error('Failed to start HTTP server', { error });
      throw error;
    }
  }

  /**
   * Stop the HTTP server
   */
  async stop(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.server) {
        this.server.close((error: any) => {
          if (error) {
            logger.error('Error stopping HTTP server', { error });
            reject(error);
          } else {
            logger.info('HTTP Server stopped');
            resolve();
          }
        });
      } else {
        resolve();
      }
    });
  }
}

// Export singleton instance
export const httpServer = new HttpServer();
