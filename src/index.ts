#!/usr/bin/env node

import { logger } from './utils/logger';
import { config } from './utils/config';
import { httpServer } from './server/http-server';
import { mcpServer } from './mcp/server';

/**
 * Main application class
 */
class PayrollMCPApplication {
  private isShuttingDown = false;

  /**
   * Start the application
   */
  async start(): Promise<void> {
    try {
      logger.info('Starting Payroll MCP Server', {
        version: '1.0.0',
        nodeEnv: config.server.nodeEnv,
        port: config.server.port
      });

      // Setup graceful shutdown handlers
      this.setupShutdownHandlers();

      // Determine which transport to use based on environment
      const transport = process.env['MCP_TRANSPORT'] || 'http';

      if (transport === 'stdio') {
        // Start MCP server with stdio transport
        await mcpServer.start();
        logger.info('Application started with stdio transport');
      } else {
        // Start HTTP server with streamable HTTP transport
        await httpServer.start();
        logger.info('Application started with HTTP transport');
      }

    } catch (error) {
      logger.error('Failed to start application', { error });
      process.exit(1);
    }
  }

  /**
   * Stop the application gracefully
   */
  async stop(): Promise<void> {
    if (this.isShuttingDown) {
      logger.warn('Shutdown already in progress');
      return;
    }

    this.isShuttingDown = true;
    logger.info('Shutting down application...');

    try {
      // Stop servers
      await Promise.all([
        httpServer.stop(),
        mcpServer.stop()
      ]);

      logger.info('Application shutdown complete');
      process.exit(0);

    } catch (error) {
      logger.error('Error during shutdown', { error });
      process.exit(1);
    }
  }

  /**
   * Setup graceful shutdown signal handlers
   */
  private setupShutdownHandlers(): void {
    const signals = ['SIGTERM', 'SIGINT', 'SIGUSR2'];

    signals.forEach(signal => {
      process.on(signal, async () => {
        logger.info(`Received ${signal}, starting graceful shutdown`);
        await this.stop();
      });
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('Uncaught exception', { error });
      this.stop();
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      logger.error('Unhandled promise rejection', { reason, promise });
      this.stop();
    });
  }
}

// Start the application if this file is run directly
if (require.main === module) {
  const app = new PayrollMCPApplication();
  app.start().catch((error) => {
    logger.error('Application startup failed', { error });
    process.exit(1);
  });
}

export { PayrollMCPApplication };
