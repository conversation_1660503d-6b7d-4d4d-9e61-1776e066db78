import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { 
  CallToolRequestSchema,
  ListToolsRequestSchema,
  Tool
} from '@modelcontextprotocol/sdk/types.js';
import { logger } from '../utils/logger';
import { payrollTool, handlePayrollTool } from './payroll-tool';

/**
 * MCP Server implementation for payroll data
 */
export class PayrollMCPServer {
  private server: Server;
  private tools: Tool[] = [payrollTool];

  constructor() {
    this.server = new Server(
      {
        name: 'payroll-mcp-server',
        version: '1.0.0'
      },
      {
        capabilities: {
          tools: {}
        }
      }
    );

    this.setupHandlers();
  }

  /**
   * Setup MCP server handlers
   */
  private setupHandlers(): void {
    // List tools handler
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      logger.debug('Listing available tools');
      return {
        tools: this.tools
      };
    });

    // Call tool handler
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name } = request.params;
      
      logger.info('Tool call received', { toolName: name });

      switch (name) {
        case 'payroll':
          // Extract access token from request context or headers
          // Note: In a real implementation, you'd need to pass the access token
          // through the MCP protocol or establish it during connection
          const accessToken = this.extractAccessToken(request);
          
          if (!accessToken) {
            return {
              content: [{
                type: 'text',
                text: 'Authentication required: No access token provided'
              }],
              isError: true
            };
          }

          return await handlePayrollTool(request, accessToken);

        default:
          logger.warn('Unknown tool requested', { toolName: name });
          return {
            content: [{
              type: 'text',
              text: `Unknown tool: ${name}`
            }],
            isError: true
          };
      }
    });

    // Error handler
    this.server.onerror = (error) => {
      logger.error('MCP Server error', { error });
    };
  }

  /**
   * Extract access token from request
   * Note: This is a placeholder implementation
   * In practice, you'd need to establish authentication during connection setup
   */
  private extractAccessToken(_request: any): string | null {
    // This would need to be implemented based on how authentication
    // is handled in your MCP transport layer
    // For now, return null to indicate missing authentication
    return null;
  }

  /**
   * Start the MCP server with stdio transport
   */
  async start(): Promise<void> {
    try {
      const transport = new StdioServerTransport();
      await this.server.connect(transport);
      
      logger.info('MCP Server started with stdio transport');
    } catch (error) {
      logger.error('Failed to start MCP server', { error });
      throw error;
    }
  }

  /**
   * Stop the MCP server
   */
  async stop(): Promise<void> {
    try {
      await this.server.close();
      logger.info('MCP Server stopped');
    } catch (error) {
      logger.error('Error stopping MCP server', { error });
      throw error;
    }
  }
}

// Export singleton instance
export const mcpServer = new PayrollMCPServer();
