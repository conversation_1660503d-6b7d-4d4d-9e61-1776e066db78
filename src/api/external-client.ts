import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { config } from '../utils/config';
import { logger } from '../utils/logger';
import { 
  PayrollParameters, 
  JsonRpcRequest, 
  JsonRpcResponse, 
  PayrollResult 
} from '../types/payroll';

/**
 * Client for communicating with external payroll JSON-RPC API
 */
export class ExternalApiClient {
  private httpClient: AxiosInstance;
  private requestId: number = 1;

  constructor() {
    this.httpClient = axios.create({
      baseURL: config.externalApi.url,
      timeout: config.externalApi.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    // Request interceptor for logging
    this.httpClient.interceptors.request.use(
      (config) => {
        logger.debug('External API request', {
          method: config.method,
          url: config.url,
          data: config.data
        });
        return config;
      },
      (error) => {
        logger.error('External API request error', { error });
        return Promise.reject(error);
      }
    );

    // Response interceptor for logging
    this.httpClient.interceptors.response.use(
      (response) => {
        logger.debug('External API response', {
          status: response.status,
          data: response.data
        });
        return response;
      },
      (error) => {
        logger.error('External API response error', {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message
        });
        return Promise.reject(error);
      }
    );
  }

  /**
   * Make a payroll grid request to the external API
   */
  async getPayrollGrid(
    parameters: PayrollParameters,
    page: number = 1,
    rows: number = 30,
    sort: string = 'owner_names',
    order: string = 'asc',
    accessToken: string
  ): Promise<PayrollResult> {
    try {
      const requestId = this.getNextRequestId();
      
      const jsonRpcRequest: JsonRpcRequest = {
        method: 'read',
        params: [parameters, page, rows, sort, order],
        id: requestId,
        jsonrpc: '2.0'
      };

      logger.info('Making payroll grid request', {
        requestId,
        parameters: this.sanitizeParameters(parameters),
        page,
        rows,
        sort,
        order
      });

      const response: AxiosResponse<JsonRpcResponse> = await this.httpClient.post(
        '/index.php?payroll-rpc=payroll-grid',
        jsonRpcRequest,
        {
          headers: {
            'Authorization': `Bearer ${accessToken}`
          }
        }
      );

      const jsonRpcResponse = response.data;

      // Check for JSON-RPC errors
      if (jsonRpcResponse.error) {
        logger.error('JSON-RPC error response', {
          requestId,
          error: jsonRpcResponse.error
        });
        throw new Error(`JSON-RPC Error: ${jsonRpcResponse.error.message}`);
      }

      // Validate response structure
      if (!jsonRpcResponse.result) {
        throw new Error('Invalid JSON-RPC response: missing result');
      }

      logger.info('Payroll grid request successful', {
        requestId,
        totalRecords: jsonRpcResponse.result.total,
        recordsReturned: jsonRpcResponse.result.rows.length
      });

      return jsonRpcResponse.result;

    } catch (error) {
      logger.error('Payroll grid request failed', {
        error: error instanceof Error ? error.message : error,
        parameters: this.sanitizeParameters(parameters)
      });

      if (axios.isAxiosError(error)) {
        if (error.response?.status === 401) {
          throw new Error('Authentication failed: Invalid or expired access token');
        } else if (error.response?.status === 403) {
          throw new Error('Authorization failed: Insufficient permissions');
        } else if (error.response && error.response.status >= 500) {
          throw new Error('External API server error');
        }
      }

      throw error;
    }
  }

  /**
   * Get next request ID for JSON-RPC
   */
  private getNextRequestId(): number {
    return this.requestId++;
  }

  /**
   * Sanitize parameters for logging (remove sensitive data)
   */
  private sanitizeParameters(parameters: PayrollParameters): Partial<PayrollParameters> {
    const sanitized = { ...parameters };
    
    // Remove or mask sensitive fields if any
    if (sanitized.egn) {
      sanitized.egn = sanitized.egn.substring(0, 3) + '***';
    }
    
    if (sanitized.owner_egns) {
      sanitized.owner_egns = sanitized.owner_egns.map(egn => 
        egn.substring(0, 3) + '***'
      );
    }

    return sanitized;
  }
}

// Export singleton instance
export const externalApiClient = new ExternalApiClient();
