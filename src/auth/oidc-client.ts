import { Issuer, Client, TokenSet as OIDCTokenSet } from 'openid-client';
import { config } from '../utils/config';
import { logger } from '../utils/logger';
import { TokenSet, UserInfo, AuthError } from '../types/auth';

/**
 * OIDC Client wrapper for Keycloak authentication
 */
export class OIDCClient {
  private client: Client | null = null;
  private issuer: Issuer | null = null;

  /**
   * Initialize the OIDC client
   */
  async initialize(): Promise<void> {
    try {
      logger.info('Initializing OIDC client', { issuerUrl: config.oidc.issuerUrl });
      
      this.issuer = await Issuer.discover(config.oidc.issuerUrl);
      
      this.client = new this.issuer.Client({
        client_id: config.oidc.clientId,
        client_secret: config.oidc.clientSecret,
        redirect_uris: [config.oidc.redirectUri],
        response_types: ['code']
      });

      logger.info('OIDC client initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize OIDC client', { error });
      throw new Error(`OIDC initialization failed: ${error}`);
    }
  }

  /**
   * Get authorization URL for OIDC flow
   */
  getAuthorizationUrl(state?: string): string {
    if (!this.client) {
      throw new Error('OIDC client not initialized');
    }

    return this.client.authorizationUrl({
      scope: config.oidc.scope,
      state: state || this.generateState()
    });
  }

  /**
   * Exchange authorization code for tokens
   */
  async exchangeCodeForTokens(code: string, state?: string): Promise<TokenSet> {
    if (!this.client) {
      throw new Error('OIDC client not initialized');
    }

    try {
      const callbackParams = state ? { code, state } : { code };
      const checks = state ? { state } : {};

      const tokenSet = await this.client.callback(
        config.oidc.redirectUri,
        callbackParams,
        checks
      );

      return this.convertTokenSet(tokenSet);
    } catch (error) {
      logger.error('Token exchange failed', { error, code });
      throw new AuthError('TOKEN_EXCHANGE_FAILED', 'Failed to exchange code for tokens');
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshTokens(refreshToken: string): Promise<TokenSet> {
    if (!this.client) {
      throw new Error('OIDC client not initialized');
    }

    try {
      const tokenSet = await this.client.refresh(refreshToken);
      return this.convertTokenSet(tokenSet);
    } catch (error) {
      logger.error('Token refresh failed', { error });
      throw new AuthError('TOKEN_REFRESH_FAILED', 'Failed to refresh tokens');
    }
  }

  /**
   * Get user info using access token
   */
  async getUserInfo(accessToken: string): Promise<UserInfo> {
    if (!this.client) {
      throw new Error('OIDC client not initialized');
    }

    try {
      const userInfo = await this.client.userinfo(accessToken);
      return userInfo as UserInfo;
    } catch (error) {
      logger.error('Failed to get user info', { error });
      throw new AuthError('USERINFO_FAILED', 'Failed to get user information');
    }
  }

  /**
   * Validate access token
   */
  async validateToken(accessToken: string): Promise<boolean> {
    try {
      await this.getUserInfo(accessToken);
      return true;
    } catch (error) {
      logger.warn('Token validation failed', { error });
      return false;
    }
  }

  /**
   * Generate random state for OIDC flow
   */
  private generateState(): string {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
  }

  /**
   * Convert OIDC TokenSet to our TokenSet interface
   */
  private convertTokenSet(tokenSet: OIDCTokenSet): TokenSet {
    return {
      access_token: tokenSet.access_token!,
      refresh_token: tokenSet.refresh_token || undefined,
      id_token: tokenSet.id_token || undefined,
      token_type: tokenSet.token_type || 'Bearer',
      expires_at: tokenSet.expires_at || undefined,
      scope: tokenSet.scope || undefined
    };
  }
}

// Export singleton instance
export const oidcClient = new OIDCClient();
