"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.externalApiClient = exports.ExternalApiClient = void 0;
const axios_1 = __importDefault(require("axios"));
const config_1 = require("../utils/config");
const logger_1 = require("../utils/logger");
/**
 * Client for communicating with external payroll JSON-RPC API
 */
class ExternalApiClient {
    httpClient;
    requestId = 1;
    constructor() {
        this.httpClient = axios_1.default.create({
            baseURL: config_1.config.externalApi.url,
            timeout: config_1.config.externalApi.timeout,
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        });
        // Request interceptor for logging
        this.httpClient.interceptors.request.use((config) => {
            logger_1.logger.debug('External API request', {
                method: config.method,
                url: config.url,
                data: config.data
            });
            return config;
        }, (error) => {
            logger_1.logger.error('External API request error', { error });
            return Promise.reject(error);
        });
        // Response interceptor for logging
        this.httpClient.interceptors.response.use((response) => {
            logger_1.logger.debug('External API response', {
                status: response.status,
                data: response.data
            });
            return response;
        }, (error) => {
            logger_1.logger.error('External API response error', {
                status: error.response?.status,
                data: error.response?.data,
                message: error.message
            });
            return Promise.reject(error);
        });
    }
    /**
     * Make a payroll grid request to the external API
     */
    async getPayrollGrid(parameters, page = 1, rows = 30, sort = 'owner_names', order = 'asc', accessToken) {
        try {
            const requestId = this.getNextRequestId();
            const jsonRpcRequest = {
                method: 'read',
                params: [parameters, page, rows, sort, order],
                id: requestId,
                jsonrpc: '2.0'
            };
            logger_1.logger.info('Making payroll grid request', {
                requestId,
                parameters: this.sanitizeParameters(parameters),
                page,
                rows,
                sort,
                order
            });
            const response = await this.httpClient.post('/index.php?payroll-rpc=payroll-grid', jsonRpcRequest, {
                headers: {
                    'Authorization': `Bearer ${accessToken}`
                }
            });
            const jsonRpcResponse = response.data;
            // Check for JSON-RPC errors
            if (jsonRpcResponse.error) {
                logger_1.logger.error('JSON-RPC error response', {
                    requestId,
                    error: jsonRpcResponse.error
                });
                throw new Error(`JSON-RPC Error: ${jsonRpcResponse.error.message}`);
            }
            // Validate response structure
            if (!jsonRpcResponse.result) {
                throw new Error('Invalid JSON-RPC response: missing result');
            }
            logger_1.logger.info('Payroll grid request successful', {
                requestId,
                totalRecords: jsonRpcResponse.result.total,
                recordsReturned: jsonRpcResponse.result.rows.length
            });
            return jsonRpcResponse.result;
        }
        catch (error) {
            logger_1.logger.error('Payroll grid request failed', {
                error: error instanceof Error ? error.message : error,
                parameters: this.sanitizeParameters(parameters)
            });
            if (axios_1.default.isAxiosError(error)) {
                if (error.response?.status === 401) {
                    throw new Error('Authentication failed: Invalid or expired access token');
                }
                else if (error.response?.status === 403) {
                    throw new Error('Authorization failed: Insufficient permissions');
                }
                else if (error.response && error.response.status >= 500) {
                    throw new Error('External API server error');
                }
            }
            throw error;
        }
    }
    /**
     * Get next request ID for JSON-RPC
     */
    getNextRequestId() {
        return this.requestId++;
    }
    /**
     * Sanitize parameters for logging (remove sensitive data)
     */
    sanitizeParameters(parameters) {
        const sanitized = { ...parameters };
        // Remove or mask sensitive fields if any
        if (sanitized.egn) {
            sanitized.egn = sanitized.egn.substring(0, 3) + '***';
        }
        if (sanitized.owner_egns) {
            sanitized.owner_egns = sanitized.owner_egns.map(egn => egn.substring(0, 3) + '***');
        }
        return sanitized;
    }
}
exports.ExternalApiClient = ExternalApiClient;
// Export singleton instance
exports.externalApiClient = new ExternalApiClient();
//# sourceMappingURL=external-client.js.map