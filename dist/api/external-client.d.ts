import { PayrollParameters, PayrollResult } from '../types/payroll';
/**
 * Client for communicating with external payroll JSON-RPC API
 */
export declare class ExternalApiClient {
    private httpClient;
    private requestId;
    constructor();
    /**
     * Make a payroll grid request to the external API
     */
    getPayrollGrid(parameters: PayrollParameters, page: number | undefined, rows: number | undefined, sort: string | undefined, order: string | undefined, accessToken: string): Promise<PayrollResult>;
    /**
     * Get next request ID for JSON-RPC
     */
    private getNextRequestId;
    /**
     * Sanitize parameters for logging (remove sensitive data)
     */
    private sanitizeParameters;
}
export declare const externalApiClient: ExternalApiClient;
//# sourceMappingURL=external-client.d.ts.map