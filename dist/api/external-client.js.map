{"version": 3, "file": "external-client.js", "sourceRoot": "", "sources": ["../../src/api/external-client.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA4D;AAC5D,4CAAyC;AACzC,4CAAyC;AAQzC;;GAEG;AACH,MAAa,iBAAiB;IACpB,UAAU,CAAgB;IAC1B,SAAS,GAAW,CAAC,CAAC;IAE9B;QACE,IAAI,CAAC,UAAU,GAAG,eAAK,CAAC,MAAM,CAAC;YAC7B,OAAO,EAAE,eAAM,CAAC,WAAW,CAAC,GAAG;YAC/B,OAAO,EAAE,eAAM,CAAC,WAAW,CAAC,OAAO;YACnC,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;gBAClC,QAAQ,EAAE,kBAAkB;aAC7B;SACF,CAAC,CAAC;QAEH,kCAAkC;QAClC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CACtC,CAAC,MAAM,EAAE,EAAE;YACT,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE;gBACnC,MAAM,EAAE,MAAM,CAAC,MAAM;gBACrB,GAAG,EAAE,MAAM,CAAC,GAAG;gBACf,IAAI,EAAE,MAAM,CAAC,IAAI;aAClB,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACtD,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CACF,CAAC;QAEF,mCAAmC;QACnC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACvC,CAAC,QAAQ,EAAE,EAAE;YACX,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE;gBACpC,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,IAAI,EAAE,QAAQ,CAAC,IAAI;aACpB,CAAC,CAAC;YACH,OAAO,QAAQ,CAAC;QAClB,CAAC,EACD,CAAC,KAAK,EAAE,EAAE;YACR,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC1C,MAAM,EAAE,KAAK,CAAC,QAAQ,EAAE,MAAM;gBAC9B,IAAI,EAAE,KAAK,CAAC,QAAQ,EAAE,IAAI;gBAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;aACvB,CAAC,CAAC;YACH,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,cAAc,CAClB,UAA6B,EAC7B,OAAe,CAAC,EAChB,OAAe,EAAE,EACjB,OAAe,aAAa,EAC5B,QAAgB,KAAK,EACrB,WAAmB;QAEnB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAC;YAE1C,MAAM,cAAc,GAAmB;gBACrC,MAAM,EAAE,MAAM;gBACd,MAAM,EAAE,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;gBAC7C,EAAE,EAAE,SAAS;gBACb,OAAO,EAAE,KAAK;aACf,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;gBACzC,SAAS;gBACT,UAAU,EAAE,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC;gBAC/C,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,KAAK;aACN,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAmC,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CACzE,qCAAqC,EACrC,cAAc,EACd;gBACE,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,WAAW,EAAE;iBACzC;aACF,CACF,CAAC;YAEF,MAAM,eAAe,GAAG,QAAQ,CAAC,IAAI,CAAC;YAEtC,4BAA4B;YAC5B,IAAI,eAAe,CAAC,KAAK,EAAE,CAAC;gBAC1B,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;oBACtC,SAAS;oBACT,KAAK,EAAE,eAAe,CAAC,KAAK;iBAC7B,CAAC,CAAC;gBACH,MAAM,IAAI,KAAK,CAAC,mBAAmB,eAAe,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;YACtE,CAAC;YAED,8BAA8B;YAC9B,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAC;YAC/D,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE;gBAC7C,SAAS;gBACT,YAAY,EAAE,eAAe,CAAC,MAAM,CAAC,KAAK;gBAC1C,eAAe,EAAE,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM;aACpD,CAAC,CAAC;YAEH,OAAO,eAAe,CAAC,MAAM,CAAC;QAEhC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;gBAC1C,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;gBACrD,UAAU,EAAE,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC;aAChD,CAAC,CAAC;YAEH,IAAI,eAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC9B,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;oBACnC,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;gBAC5E,CAAC;qBAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;oBAC1C,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;gBACpE,CAAC;qBAAM,IAAI,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;oBAC1D,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;gBAC/C,CAAC;YACH,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACK,gBAAgB;QACtB,OAAO,IAAI,CAAC,SAAS,EAAE,CAAC;IAC1B,CAAC;IAED;;OAEG;IACK,kBAAkB,CAAC,UAA6B;QACtD,MAAM,SAAS,GAAG,EAAE,GAAG,UAAU,EAAE,CAAC;QAEpC,yCAAyC;QACzC,IAAI,SAAS,CAAC,GAAG,EAAE,CAAC;YAClB,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC;QACxD,CAAC;QAED,IAAI,SAAS,CAAC,UAAU,EAAE,CAAC;YACzB,SAAS,CAAC,UAAU,GAAG,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CACpD,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAC5B,CAAC;QACJ,CAAC;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF;AAhKD,8CAgKC;AAED,4BAA4B;AACf,QAAA,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC"}