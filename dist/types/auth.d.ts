/**
 * Authentication and OIDC related types
 */
export interface OIDCConfig {
    issuerUrl: string;
    clientId: string;
    clientSecret: string;
    redirectUri: string;
    scope: string;
}
export interface TokenSet {
    access_token: string;
    refresh_token?: string | undefined;
    id_token?: string | undefined;
    token_type: string;
    expires_at?: number | undefined;
    scope?: string | undefined;
}
export interface UserInfo {
    sub: string;
    name?: string;
    given_name?: string;
    family_name?: string;
    email?: string;
    email_verified?: boolean;
    preferred_username?: string;
    [key: string]: any;
}
export interface AuthenticatedUser {
    id: string;
    username: string;
    email?: string | undefined;
    name?: string | undefined;
    tokenSet: TokenSet;
    userInfo: UserInfo;
}
export declare class AuthError extends Error {
    code: string;
    details?: any;
    constructor(code: string, message: string, details?: any);
}
export interface AuthMiddlewareOptions {
    requireAuth?: boolean;
    allowedScopes?: string[];
    allowedRoles?: string[];
}
//# sourceMappingURL=auth.d.ts.map