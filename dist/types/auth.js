"use strict";
/**
 * Authentication and OIDC related types
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthError = void 0;
class AuthError extends Error {
    code;
    details;
    constructor(code, message, details) {
        super(message);
        this.name = 'AuthError';
        this.code = code;
        this.details = details;
    }
}
exports.AuthError = AuthError;
//# sourceMappingURL=auth.js.map