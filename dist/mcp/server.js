"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mcpServer = exports.PayrollMCPServer = void 0;
const index_js_1 = require("@modelcontextprotocol/sdk/server/index.js");
const stdio_js_1 = require("@modelcontextprotocol/sdk/server/stdio.js");
const types_js_1 = require("@modelcontextprotocol/sdk/types.js");
const logger_1 = require("../utils/logger");
const payroll_tool_1 = require("./payroll-tool");
/**
 * MCP Server implementation for payroll data
 */
class PayrollMCPServer {
    server;
    tools = [payroll_tool_1.payrollTool];
    constructor() {
        this.server = new index_js_1.Server({
            name: 'payroll-mcp-server',
            version: '1.0.0'
        }, {
            capabilities: {
                tools: {}
            }
        });
        this.setupHandlers();
    }
    /**
     * Setup MCP server handlers
     */
    setupHandlers() {
        // List tools handler
        this.server.setRequestHandler(types_js_1.ListToolsRequestSchema, async () => {
            logger_1.logger.debug('Listing available tools');
            return {
                tools: this.tools
            };
        });
        // Call tool handler
        this.server.setRequestHandler(types_js_1.CallToolRequestSchema, async (request) => {
            const { name } = request.params;
            logger_1.logger.info('Tool call received', { toolName: name });
            switch (name) {
                case 'payroll':
                    // Extract access token from request context or headers
                    // Note: In a real implementation, you'd need to pass the access token
                    // through the MCP protocol or establish it during connection
                    const accessToken = this.extractAccessToken(request);
                    if (!accessToken) {
                        return {
                            content: [{
                                    type: 'text',
                                    text: 'Authentication required: No access token provided'
                                }],
                            isError: true
                        };
                    }
                    return await (0, payroll_tool_1.handlePayrollTool)(request, accessToken);
                default:
                    logger_1.logger.warn('Unknown tool requested', { toolName: name });
                    return {
                        content: [{
                                type: 'text',
                                text: `Unknown tool: ${name}`
                            }],
                        isError: true
                    };
            }
        });
        // Error handler
        this.server.onerror = (error) => {
            logger_1.logger.error('MCP Server error', { error });
        };
    }
    /**
     * Extract access token from request
     * Note: This is a placeholder implementation
     * In practice, you'd need to establish authentication during connection setup
     */
    extractAccessToken(_request) {
        // This would need to be implemented based on how authentication
        // is handled in your MCP transport layer
        // For now, return null to indicate missing authentication
        return null;
    }
    /**
     * Start the MCP server with stdio transport
     */
    async start() {
        try {
            const transport = new stdio_js_1.StdioServerTransport();
            await this.server.connect(transport);
            logger_1.logger.info('MCP Server started with stdio transport');
        }
        catch (error) {
            logger_1.logger.error('Failed to start MCP server', { error });
            throw error;
        }
    }
    /**
     * Stop the MCP server
     */
    async stop() {
        try {
            await this.server.close();
            logger_1.logger.info('MCP Server stopped');
        }
        catch (error) {
            logger_1.logger.error('Error stopping MCP server', { error });
            throw error;
        }
    }
}
exports.PayrollMCPServer = PayrollMCPServer;
// Export singleton instance
exports.mcpServer = new PayrollMCPServer();
//# sourceMappingURL=server.js.map