{"version": 3, "file": "http-server.js", "sourceRoot": "", "sources": ["../../src/server/http-server.ts"], "names": [], "mappings": ";;;;;;AAAA,sDAA8D;AAC9D,gDAAwB;AACxB,oDAA4B;AAC5B,4CAAyC;AACzC,4CAAwD;AACxD,qDAAiD;AACjD,6CAA+D;AAC/D,sDAAqE;AAGrE;;GAEG;AACH,MAAa,UAAU;IACb,GAAG,CAAU;IACb,MAAM,CAAM;IAEpB;QACE,IAAI,CAAC,GAAG,GAAG,IAAA,iBAAO,GAAE,CAAC;QACrB,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED;;OAEG;IACK,eAAe;QACrB,sBAAsB;QACtB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAA,gBAAM,GAAE,CAAC,CAAC;QAEvB,kBAAkB;QAClB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAA,cAAI,EAAC;YAChB,MAAM,EAAE,eAAM,CAAC,MAAM,CAAC,UAAU;YAChC,WAAW,EAAE,eAAM,CAAC,MAAM,CAAC,eAAe;SAC3C,CAAC,CAAC,CAAC;QAEJ,0BAA0B;QAC1B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;QAC9C,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAErD,kBAAkB;QAClB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,sBAAa,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,WAAW;QACjB,wBAAwB;QACxB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,IAAa,EAAE,GAAa,EAAE,EAAE;YACvD,GAAG,CAAC,IAAI,CAAC;gBACP,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,OAAO;aACjB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,6BAA6B;QAC7B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YAC1D,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAW,CAAC;gBAC3C,MAAM,OAAO,GAAG,wBAAU,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;gBACtD,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACxB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;gBACjD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,oCAAoC,EAAE,CAAC,CAAC;YACxE,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAiB,EAAE;YAClF,IAAI,CAAC;gBACH,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,KAAK,CAAC;gBAElC,IAAI,CAAC,IAAI,EAAE,CAAC;oBACV,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,4BAA4B,EAAE,CAAC,CAAC;oBAC9D,OAAO;gBACT,CAAC;gBAED,MAAM,QAAQ,GAAG,MAAM,wBAAU,CAAC,qBAAqB,CACrD,IAAc,EACd,KAAe,CAChB,CAAC;gBAEF,2DAA2D;gBAC3D,uDAAuD;gBACvD,GAAG,CAAC,IAAI,CAAC;oBACP,OAAO,EAAE,2BAA2B;oBACpC,YAAY,EAAE,QAAQ,CAAC,YAAY;oBACnC,UAAU,EAAE,QAAQ,CAAC,UAAU;iBAChC,CAAC,CAAC;YAEL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;gBAC1D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,uBAAuB,EAAE,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,4CAA4C;QAC5C,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,YAAY,EAAE,mBAAY,EAAE,CAAC,IAAa,EAAE,GAAa,EAAE,EAAE;YACxE,GAAG,CAAC,IAAI,CAAC;gBACP,KAAK,EAAE,CAAC,0BAAW,CAAC;aACrB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,oCAAoC;QACpC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,sBAAsB,EAAE,kBAAW,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAiB,EAAE;YACtG,IAAI,CAAC;gBACH,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;gBAChC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;gBAEzC,IAAI,QAAQ,KAAK,SAAS,EAAE,CAAC;oBAC3B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBACnB,KAAK,EAAE,gBAAgB;wBACvB,OAAO,EAAE,SAAS,QAAQ,aAAa;qBACxC,CAAC,CAAC;oBACH,OAAO;gBACT,CAAC;gBAED,0BAA0B;gBAC1B,MAAM,WAAW,GAAoB;oBACnC,MAAM,EAAE,YAAY;oBACpB,MAAM,EAAE;wBACN,IAAI,EAAE,QAAQ;wBACd,SAAS,EAAE,QAAQ;qBACpB;iBACF,CAAC;gBAEF,wCAAwC;gBACxC,MAAM,MAAM,GAAG,MAAM,IAAA,gCAAiB,EACpC,WAAW,EACX,GAAG,CAAC,IAAK,CAAC,QAAQ,CAAC,YAAY,CAChC,CAAC;gBAEF,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;oBACnB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBACnB,KAAK,EAAE,sBAAsB;wBAC7B,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,uBAAuB;qBAC5D,CAAC,CAAC;oBACH,OAAO;gBACT,CAAC;gBAED,MAAM,UAAU,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;gBAC3C,GAAG,CAAC,IAAI,CAAC;oBACP,MAAM,EAAE,UAAU,IAAI,OAAO,UAAU,KAAK,QAAQ,IAAI,UAAU,CAAC,IAAI,EAAE;wBACvE,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC;wBACxB,CAAC,CAAC,IAAI;iBACT,CAAC,CAAC;YAEL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;gBACnF,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,gBAAgB;oBACvB,OAAO,EAAE,uBAAuB;iBACjC,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,yCAAyC;QACzC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,EAAE,kBAAW,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;YAC9E,IAAI,CAAC;gBACH,qCAAqC;gBACrC,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,kBAAkB,CAAC,CAAC;gBAClD,GAAG,CAAC,SAAS,CAAC,mBAAmB,EAAE,SAAS,CAAC,CAAC;gBAC9C,GAAG,CAAC,SAAS,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;gBAC3C,GAAG,CAAC,SAAS,CAAC,YAAY,EAAE,YAAY,CAAC,CAAC;gBAE1C,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;gBAEpC,QAAQ,MAAM,EAAE,CAAC;oBACf,KAAK,YAAY;wBACf,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;4BACvB,OAAO,EAAE,KAAK;4BACd,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC;4BACnB,MAAM,EAAE,EAAE,KAAK,EAAE,CAAC,0BAAW,CAAC,EAAE;yBACjC,CAAC,CAAC,CAAC;wBACJ,MAAM;oBAER,KAAK,YAAY;wBACf,MAAM,WAAW,GAAoB;4BACnC,MAAM,EAAE,YAAY;4BACpB,MAAM,EAAE,MAAM;yBACf,CAAC;wBAEF,MAAM,MAAM,GAAG,MAAM,IAAA,gCAAiB,EACpC,WAAW,EACX,GAAG,CAAC,IAAK,CAAC,QAAQ,CAAC,YAAY,CAChC,CAAC;wBAEF,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;4BACvB,OAAO,EAAE,KAAK;4BACd,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC;4BACnB,MAAM,EAAE,MAAM;yBACf,CAAC,CAAC,CAAC;wBACJ,MAAM;oBAER;wBACE,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;4BACvB,OAAO,EAAE,KAAK;4BACd,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC;4BACnB,KAAK,EAAE;gCACL,IAAI,EAAE,CAAC,KAAK;gCACZ,OAAO,EAAE,kBAAkB;6BAC5B;yBACF,CAAC,CAAC,CAAC;gBACR,CAAC;gBAED,GAAG,CAAC,GAAG,EAAE,CAAC;YAEZ,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;gBACpD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,EAAE,EAAE,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,IAAI,CAAC;oBAC5B,KAAK,EAAE;wBACL,IAAI,EAAE,CAAC,KAAK;wBACZ,OAAO,EAAE,gBAAgB;qBAC1B;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,cAAc;QACd,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,IAAa,EAAE,GAAa,EAAE,EAAE;YACjD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,WAAW;gBAClB,OAAO,EAAE,oBAAoB;aAC9B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,gBAAgB;QAChB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,KAAU,EAAE,GAAY,EAAE,GAAa,EAAE,KAAU,EAAE,EAAE;YACnE,eAAM,CAAC,KAAK,CAAC,iBAAiB,EAAE,EAAE,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;YAC7E,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,gBAAgB;gBACvB,OAAO,EAAE,uBAAuB;aACjC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,CAAC;YACH,yBAAyB;YACzB,MAAM,wBAAU,CAAC,UAAU,EAAE,CAAC;YAE9B,oBAAoB;YACpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,eAAM,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE;gBACrD,eAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE;oBACjC,IAAI,EAAE,eAAM,CAAC,MAAM,CAAC,IAAI;oBACxB,OAAO,EAAE,eAAM,CAAC,MAAM,CAAC,OAAO;iBAC/B,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QAEL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACvD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,IAAI;QACR,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,KAAU,EAAE,EAAE;oBAC/B,IAAI,KAAK,EAAE,CAAC;wBACV,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;wBACtD,MAAM,CAAC,KAAK,CAAC,CAAC;oBAChB,CAAC;yBAAM,CAAC;wBACN,eAAM,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;wBACnC,OAAO,EAAE,CAAC;oBACZ,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AA5QD,gCA4QC;AAED,4BAA4B;AACf,QAAA,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC"}