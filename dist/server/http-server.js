"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.httpServer = exports.HttpServer = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const config_1 = require("../utils/config");
const logger_1 = require("../utils/logger");
const oidc_client_1 = require("../auth/oidc-client");
const auth_1 = require("../middleware/auth");
const payroll_tool_1 = require("../mcp/payroll-tool");
/**
 * HTTP Server with Streamable HTTP transport for MCP
 */
class HttpServer {
    app;
    server;
    constructor() {
        this.app = (0, express_1.default)();
        this.setupMiddleware();
        this.setupRoutes();
    }
    /**
     * Setup Express middleware
     */
    setupMiddleware() {
        // Security middleware
        this.app.use((0, helmet_1.default)());
        // CORS middleware
        this.app.use((0, cors_1.default)({
            origin: config_1.config.server.corsOrigin,
            credentials: config_1.config.server.corsCredentials
        }));
        // Body parsing middleware
        this.app.use(express_1.default.json({ limit: '10mb' }));
        this.app.use(express_1.default.urlencoded({ extended: true }));
        // Request logging
        this.app.use(logger_1.requestLogger);
    }
    /**
     * Setup Express routes
     */
    setupRoutes() {
        // Health check endpoint
        this.app.get('/health', (_req, res) => {
            res.json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                version: '1.0.0'
            });
        });
        // OIDC authentication routes
        this.app.get('/auth/login', (req, res) => {
            try {
                const state = req.query['state'];
                const authUrl = oidc_client_1.oidcClient.getAuthorizationUrl(state);
                res.redirect(authUrl);
            }
            catch (error) {
                logger_1.logger.error('Login redirect failed', { error });
                res.status(500).json({ error: 'Authentication service unavailable' });
            }
        });
        this.app.get('/auth/callback', async (req, res) => {
            try {
                const { code, state } = req.query;
                if (!code) {
                    res.status(400).json({ error: 'Authorization code missing' });
                    return;
                }
                const tokenSet = await oidc_client_1.oidcClient.exchangeCodeForTokens(code, state);
                // In a real implementation, you'd store the token securely
                // and return it to the client or redirect with success
                res.json({
                    message: 'Authentication successful',
                    access_token: tokenSet.access_token,
                    expires_at: tokenSet.expires_at
                });
            }
            catch (error) {
                logger_1.logger.error('Authentication callback failed', { error });
                res.status(400).json({ error: 'Authentication failed' });
            }
        });
        // MCP Tools endpoint - List available tools
        this.app.get('/mcp/tools', auth_1.optionalAuth, (_req, res) => {
            res.json({
                tools: [payroll_tool_1.payrollTool]
            });
        });
        // MCP Tools endpoint - Execute tool
        this.app.post('/mcp/tools/:toolName', auth_1.requireAuth, async (req, res) => {
            try {
                const { toolName } = req.params;
                const { arguments: toolArgs } = req.body;
                if (toolName !== 'payroll') {
                    res.status(404).json({
                        error: 'TOOL_NOT_FOUND',
                        message: `Tool '${toolName}' not found`
                    });
                    return;
                }
                // Create MCP tool request
                const toolRequest = {
                    method: 'tools/call',
                    params: {
                        name: toolName,
                        arguments: toolArgs
                    }
                };
                // Execute tool with user's access token
                const result = await (0, payroll_tool_1.handlePayrollTool)(toolRequest, req.user.tokenSet.access_token);
                if (result.isError) {
                    res.status(400).json({
                        error: 'TOOL_EXECUTION_ERROR',
                        message: result.content[0]?.text || 'Tool execution failed'
                    });
                    return;
                }
                const resultText = result.content[0]?.text;
                res.json({
                    result: resultText && typeof resultText === 'string' && resultText.trim()
                        ? JSON.parse(resultText)
                        : null
                });
            }
            catch (error) {
                logger_1.logger.error('Tool execution failed', { error, toolName: req.params['toolName'] });
                res.status(500).json({
                    error: 'INTERNAL_ERROR',
                    message: 'Tool execution failed'
                });
            }
        });
        // MCP Streamable HTTP transport endpoint
        this.app.post('/mcp/stream', auth_1.requireAuth, async (req, res) => {
            try {
                // Set headers for streaming response
                res.setHeader('Content-Type', 'application/json');
                res.setHeader('Transfer-Encoding', 'chunked');
                res.setHeader('Cache-Control', 'no-cache');
                res.setHeader('Connection', 'keep-alive');
                const { method, params } = req.body;
                switch (method) {
                    case 'tools/list':
                        res.write(JSON.stringify({
                            jsonrpc: '2.0',
                            id: params?.id || 1,
                            result: { tools: [payroll_tool_1.payrollTool] }
                        }));
                        break;
                    case 'tools/call':
                        const toolRequest = {
                            method: 'tools/call',
                            params: params
                        };
                        const result = await (0, payroll_tool_1.handlePayrollTool)(toolRequest, req.user.tokenSet.access_token);
                        res.write(JSON.stringify({
                            jsonrpc: '2.0',
                            id: params?.id || 1,
                            result: result
                        }));
                        break;
                    default:
                        res.write(JSON.stringify({
                            jsonrpc: '2.0',
                            id: params?.id || 1,
                            error: {
                                code: -32601,
                                message: 'Method not found'
                            }
                        }));
                }
                res.end();
            }
            catch (error) {
                logger_1.logger.error('Streaming request failed', { error });
                res.status(500).json({
                    jsonrpc: '2.0',
                    id: req.body.params?.id || 1,
                    error: {
                        code: -32603,
                        message: 'Internal error'
                    }
                });
            }
        });
        // 404 handler
        this.app.use('*', (_req, res) => {
            res.status(404).json({
                error: 'NOT_FOUND',
                message: 'Endpoint not found'
            });
        });
        // Error handler
        this.app.use((error, req, res, _next) => {
            logger_1.logger.error('Unhandled error', { error, url: req.url, method: req.method });
            res.status(500).json({
                error: 'INTERNAL_ERROR',
                message: 'Internal server error'
            });
        });
    }
    /**
     * Start the HTTP server
     */
    async start() {
        try {
            // Initialize OIDC client
            await oidc_client_1.oidcClient.initialize();
            // Start HTTP server
            this.server = this.app.listen(config_1.config.server.port, () => {
                logger_1.logger.info('HTTP Server started', {
                    port: config_1.config.server.port,
                    nodeEnv: config_1.config.server.nodeEnv
                });
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to start HTTP server', { error });
            throw error;
        }
    }
    /**
     * Stop the HTTP server
     */
    async stop() {
        return new Promise((resolve, reject) => {
            if (this.server) {
                this.server.close((error) => {
                    if (error) {
                        logger_1.logger.error('Error stopping HTTP server', { error });
                        reject(error);
                    }
                    else {
                        logger_1.logger.info('HTTP Server stopped');
                        resolve();
                    }
                });
            }
            else {
                resolve();
            }
        });
    }
}
exports.HttpServer = HttpServer;
// Export singleton instance
exports.httpServer = new HttpServer();
//# sourceMappingURL=http-server.js.map