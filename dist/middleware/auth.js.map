{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;AAiBA,wCAyFC;AAzGD,qDAAiD;AACjD,4CAAyC;AAYzC;;GAEG;AACH,SAAgB,cAAc,CAAC,UAAiC,EAAE;IAChE,OAAO,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAiB,EAAE;QAC9E,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;YAE7C,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBACrD,IAAI,OAAO,CAAC,WAAW,KAAK,KAAK,EAAE,CAAC;oBAClC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBACnB,KAAK,EAAE,cAAc;wBACrB,OAAO,EAAE,yCAAyC;qBACnD,CAAC,CAAC;oBACH,OAAO;gBACT,CAAC;gBACD,IAAI,EAAE,CAAC;gBACP,OAAO;YACT,CAAC;YAED,MAAM,WAAW,GAAG,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,0BAA0B;YAEvE,oCAAoC;YACpC,MAAM,OAAO,GAAG,MAAM,wBAAU,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;YAE5D,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,eAAe;oBACtB,OAAO,EAAE,oCAAoC;iBAC9C,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,uBAAuB;YACvB,MAAM,QAAQ,GAAG,MAAM,wBAAU,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YAE3D,mCAAmC;YACnC,MAAM,IAAI,GAAsB;gBAC9B,EAAE,EAAE,QAAQ,CAAC,GAAG;gBAChB,QAAQ,EAAE,QAAQ,CAAC,kBAAkB,IAAI,QAAQ,CAAC,GAAG;gBACrD,KAAK,EAAE,QAAQ,CAAC,KAAK;gBACrB,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,QAAQ,EAAE;oBACR,YAAY,EAAE,WAAW;oBACzB,UAAU,EAAE,QAAQ;iBACrB;gBACD,QAAQ;aACT,CAAC;YAEF,2BAA2B;YAC3B,IAAI,OAAO,CAAC,aAAa,IAAI,OAAO,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC9D,MAAM,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;gBACvD,MAAM,gBAAgB,GAAG,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAC1D,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAC3B,CAAC;gBAEF,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACtB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBACnB,KAAK,EAAE,oBAAoB;wBAC3B,OAAO,EAAE,oCAAoC;qBAC9C,CAAC,CAAC;oBACH,OAAO;gBACT,CAAC;YACH,CAAC;YAED,0BAA0B;YAC1B,IAAI,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC5D,MAAM,SAAS,GAAG,QAAQ,CAAC,cAAc,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC;gBACxD,MAAM,eAAe,GAAG,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CACvD,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,CACzB,CAAC;gBAEF,IAAI,CAAC,eAAe,EAAE,CAAC;oBACrB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBACnB,KAAK,EAAE,mBAAmB;wBAC1B,OAAO,EAAE,mCAAmC;qBAC7C,CAAC,CAAC;oBACH,OAAO;gBACT,CAAC;YACH,CAAC;YAED,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC;YAChB,IAAI,EAAE,CAAC;QAET,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC3D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,sBAAsB;gBAC7B,OAAO,EAAE,+BAA+B;aACzC,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC;AACJ,CAAC;AAED;;GAEG;AACU,QAAA,WAAW,GAAG,cAAc,CAAC,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;AAEjE;;GAEG;AACU,QAAA,YAAY,GAAG,cAAc,CAAC,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC,CAAC"}