"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.optionalAuth = exports.requireAuth = void 0;
exports.authMiddleware = authMiddleware;
const oidc_client_1 = require("../auth/oidc-client");
const logger_1 = require("../utils/logger");
/**
 * Authentication middleware that validates OIDC tokens
 */
function authMiddleware(options = {}) {
    return async (req, res, next) => {
        try {
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                if (options.requireAuth !== false) {
                    res.status(401).json({
                        error: 'UNAUTHORIZED',
                        message: 'Missing or invalid authorization header'
                    });
                    return;
                }
                next();
                return;
            }
            const accessToken = authHeader.substring(7); // Remove 'Bearer ' prefix
            // Validate token with OIDC provider
            const isValid = await oidc_client_1.oidcClient.validateToken(accessToken);
            if (!isValid) {
                res.status(401).json({
                    error: 'INVALID_TOKEN',
                    message: 'Access token is invalid or expired'
                });
                return;
            }
            // Get user information
            const userInfo = await oidc_client_1.oidcClient.getUserInfo(accessToken);
            // Create authenticated user object
            const user = {
                id: userInfo.sub,
                username: userInfo.preferred_username || userInfo.sub,
                email: userInfo.email,
                name: userInfo.name,
                tokenSet: {
                    access_token: accessToken,
                    token_type: 'Bearer'
                },
                userInfo
            };
            // Check scopes if required
            if (options.allowedScopes && options.allowedScopes.length > 0) {
                const userScopes = userInfo['scope']?.split(' ') || [];
                const hasRequiredScope = options.allowedScopes.some(scope => userScopes.includes(scope));
                if (!hasRequiredScope) {
                    res.status(403).json({
                        error: 'INSUFFICIENT_SCOPE',
                        message: 'User does not have required scopes'
                    });
                    return;
                }
            }
            // Check roles if required
            if (options.allowedRoles && options.allowedRoles.length > 0) {
                const userRoles = userInfo['realm_access']?.roles || [];
                const hasRequiredRole = options.allowedRoles.some(role => userRoles.includes(role));
                if (!hasRequiredRole) {
                    res.status(403).json({
                        error: 'INSUFFICIENT_ROLE',
                        message: 'User does not have required roles'
                    });
                    return;
                }
            }
            req.user = user;
            next();
        }
        catch (error) {
            logger_1.logger.error('Authentication middleware error', { error });
            res.status(500).json({
                error: 'AUTHENTICATION_ERROR',
                message: 'Internal authentication error'
            });
        }
    };
}
/**
 * Middleware that requires authentication
 */
exports.requireAuth = authMiddleware({ requireAuth: true });
/**
 * Middleware that optionally authenticates
 */
exports.optionalAuth = authMiddleware({ requireAuth: false });
//# sourceMappingURL=auth.js.map