#!/usr/bin/env node
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PayrollMCPApplication = void 0;
const logger_1 = require("./utils/logger");
const config_1 = require("./utils/config");
const http_server_1 = require("./server/http-server");
const server_1 = require("./mcp/server");
/**
 * Main application class
 */
class PayrollMCPApplication {
    isShuttingDown = false;
    /**
     * Start the application
     */
    async start() {
        try {
            logger_1.logger.info('Starting Payroll MCP Server', {
                version: '1.0.0',
                nodeEnv: config_1.config.server.nodeEnv,
                port: config_1.config.server.port
            });
            // Setup graceful shutdown handlers
            this.setupShutdownHandlers();
            // Determine which transport to use based on environment
            const transport = process.env['MCP_TRANSPORT'] || 'http';
            if (transport === 'stdio') {
                // Start MCP server with stdio transport
                await server_1.mcpServer.start();
                logger_1.logger.info('Application started with stdio transport');
            }
            else {
                // Start HTTP server with streamable HTTP transport
                await http_server_1.httpServer.start();
                logger_1.logger.info('Application started with HTTP transport');
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to start application', { error });
            process.exit(1);
        }
    }
    /**
     * Stop the application gracefully
     */
    async stop() {
        if (this.isShuttingDown) {
            logger_1.logger.warn('Shutdown already in progress');
            return;
        }
        this.isShuttingDown = true;
        logger_1.logger.info('Shutting down application...');
        try {
            // Stop servers
            await Promise.all([
                http_server_1.httpServer.stop(),
                server_1.mcpServer.stop()
            ]);
            logger_1.logger.info('Application shutdown complete');
            process.exit(0);
        }
        catch (error) {
            logger_1.logger.error('Error during shutdown', { error });
            process.exit(1);
        }
    }
    /**
     * Setup graceful shutdown signal handlers
     */
    setupShutdownHandlers() {
        const signals = ['SIGTERM', 'SIGINT', 'SIGUSR2'];
        signals.forEach(signal => {
            process.on(signal, async () => {
                logger_1.logger.info(`Received ${signal}, starting graceful shutdown`);
                await this.stop();
            });
        });
        // Handle uncaught exceptions
        process.on('uncaughtException', (error) => {
            logger_1.logger.error('Uncaught exception', { error });
            this.stop();
        });
        // Handle unhandled promise rejections
        process.on('unhandledRejection', (reason, promise) => {
            logger_1.logger.error('Unhandled promise rejection', { reason, promise });
            this.stop();
        });
    }
}
exports.PayrollMCPApplication = PayrollMCPApplication;
// Start the application if this file is run directly
if (require.main === module) {
    const app = new PayrollMCPApplication();
    app.start().catch((error) => {
        logger_1.logger.error('Application startup failed', { error });
        process.exit(1);
    });
}
//# sourceMappingURL=index.js.map