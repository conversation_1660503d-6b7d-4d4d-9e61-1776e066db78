{"version": 3, "file": "oidc-client.js", "sourceRoot": "", "sources": ["../../src/auth/oidc-client.ts"], "names": [], "mappings": ";;;AAAA,iDAAyE;AACzE,4CAAyC;AACzC,4CAAyC;AACzC,wCAA8D;AAE9D;;GAEG;AACH,MAAa,UAAU;IACb,MAAM,GAAkB,IAAI,CAAC;IAC7B,MAAM,GAAkB,IAAI,CAAC;IAErC;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC;YACH,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,SAAS,EAAE,eAAM,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;YAE9E,IAAI,CAAC,MAAM,GAAG,MAAM,sBAAM,CAAC,QAAQ,CAAC,eAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAE3D,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;gBACnC,SAAS,EAAE,eAAM,CAAC,IAAI,CAAC,QAAQ;gBAC/B,aAAa,EAAE,eAAM,CAAC,IAAI,CAAC,YAAY;gBACvC,aAAa,EAAE,CAAC,eAAM,CAAC,IAAI,CAAC,WAAW,CAAC;gBACxC,cAAc,EAAE,CAAC,MAAM,CAAC;aACzB,CAAC,CAAC;YAEH,eAAM,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;QACtD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC5D,MAAM,IAAI,KAAK,CAAC,+BAA+B,KAAK,EAAE,CAAC,CAAC;QAC1D,CAAC;IACH,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,KAAc;QAChC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QAED,OAAO,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;YAClC,KAAK,EAAE,eAAM,CAAC,IAAI,CAAC,KAAK;YACxB,KAAK,EAAE,KAAK,IAAI,IAAI,CAAC,aAAa,EAAE;SACrC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,qBAAqB,CAAC,IAAY,EAAE,KAAc;QACtD,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;YAC1D,MAAM,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;YAEtC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CACzC,eAAM,CAAC,IAAI,CAAC,WAAW,EACvB,cAAc,EACd,MAAM,CACP,CAAC;YAEF,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;YACvD,MAAM,IAAI,gBAAS,CAAC,uBAAuB,EAAE,oCAAoC,CAAC,CAAC;QACrF,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,YAAoB;QACtC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;YACzD,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAChD,MAAM,IAAI,gBAAS,CAAC,sBAAsB,EAAE,0BAA0B,CAAC,CAAC;QAC1E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,WAAW,CAAC,WAAmB;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YACzD,OAAO,QAAoB,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YACnD,MAAM,IAAI,gBAAS,CAAC,iBAAiB,EAAE,gCAAgC,CAAC,CAAC;QAC3E,CAAC;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,aAAa,CAAC,WAAmB;QACrC,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YACpC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAClD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,aAAa;QACnB,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;YAC3C,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;IACrD,CAAC;IAED;;OAEG;IACK,eAAe,CAAC,QAAsB;QAC5C,OAAO;YACL,YAAY,EAAE,QAAQ,CAAC,YAAa;YACpC,aAAa,EAAE,QAAQ,CAAC,aAAa,IAAI,SAAS;YAClD,QAAQ,EAAE,QAAQ,CAAC,QAAQ,IAAI,SAAS;YACxC,UAAU,EAAE,QAAQ,CAAC,UAAU,IAAI,QAAQ;YAC3C,UAAU,EAAE,QAAQ,CAAC,UAAU,IAAI,SAAS;YAC5C,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI,SAAS;SACnC,CAAC;IACJ,CAAC;CACF;AAtID,gCAsIC;AAED,4BAA4B;AACf,QAAA,UAAU,GAAG,IAAI,UAAU,EAAE,CAAC"}