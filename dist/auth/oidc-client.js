"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.oidcClient = exports.OIDCClient = void 0;
const openid_client_1 = require("openid-client");
const config_1 = require("../utils/config");
const logger_1 = require("../utils/logger");
const auth_1 = require("../types/auth");
/**
 * OIDC Client wrapper for Keycloak authentication
 */
class OIDCClient {
    client = null;
    issuer = null;
    /**
     * Initialize the OIDC client
     */
    async initialize() {
        try {
            logger_1.logger.info('Initializing OIDC client', { issuerUrl: config_1.config.oidc.issuerUrl });
            this.issuer = await openid_client_1.Issuer.discover(config_1.config.oidc.issuerUrl);
            this.client = new this.issuer.Client({
                client_id: config_1.config.oidc.clientId,
                client_secret: config_1.config.oidc.clientSecret,
                redirect_uris: [config_1.config.oidc.redirectUri],
                response_types: ['code']
            });
            logger_1.logger.info('OIDC client initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize OIDC client', { error });
            throw new Error(`OIDC initialization failed: ${error}`);
        }
    }
    /**
     * Get authorization URL for OIDC flow
     */
    getAuthorizationUrl(state) {
        if (!this.client) {
            throw new Error('OIDC client not initialized');
        }
        return this.client.authorizationUrl({
            scope: config_1.config.oidc.scope,
            state: state || this.generateState()
        });
    }
    /**
     * Exchange authorization code for tokens
     */
    async exchangeCodeForTokens(code, state) {
        if (!this.client) {
            throw new Error('OIDC client not initialized');
        }
        try {
            const callbackParams = state ? { code, state } : { code };
            const checks = state ? { state } : {};
            const tokenSet = await this.client.callback(config_1.config.oidc.redirectUri, callbackParams, checks);
            return this.convertTokenSet(tokenSet);
        }
        catch (error) {
            logger_1.logger.error('Token exchange failed', { error, code });
            throw new auth_1.AuthError('TOKEN_EXCHANGE_FAILED', 'Failed to exchange code for tokens');
        }
    }
    /**
     * Refresh access token using refresh token
     */
    async refreshTokens(refreshToken) {
        if (!this.client) {
            throw new Error('OIDC client not initialized');
        }
        try {
            const tokenSet = await this.client.refresh(refreshToken);
            return this.convertTokenSet(tokenSet);
        }
        catch (error) {
            logger_1.logger.error('Token refresh failed', { error });
            throw new auth_1.AuthError('TOKEN_REFRESH_FAILED', 'Failed to refresh tokens');
        }
    }
    /**
     * Get user info using access token
     */
    async getUserInfo(accessToken) {
        if (!this.client) {
            throw new Error('OIDC client not initialized');
        }
        try {
            const userInfo = await this.client.userinfo(accessToken);
            return userInfo;
        }
        catch (error) {
            logger_1.logger.error('Failed to get user info', { error });
            throw new auth_1.AuthError('USERINFO_FAILED', 'Failed to get user information');
        }
    }
    /**
     * Validate access token
     */
    async validateToken(accessToken) {
        try {
            await this.getUserInfo(accessToken);
            return true;
        }
        catch (error) {
            logger_1.logger.warn('Token validation failed', { error });
            return false;
        }
    }
    /**
     * Generate random state for OIDC flow
     */
    generateState() {
        return Math.random().toString(36).substring(2, 15) +
            Math.random().toString(36).substring(2, 15);
    }
    /**
     * Convert OIDC TokenSet to our TokenSet interface
     */
    convertTokenSet(tokenSet) {
        return {
            access_token: tokenSet.access_token,
            refresh_token: tokenSet.refresh_token || undefined,
            id_token: tokenSet.id_token || undefined,
            token_type: tokenSet.token_type || 'Bearer',
            expires_at: tokenSet.expires_at || undefined,
            scope: tokenSet.scope || undefined
        };
    }
}
exports.OIDCClient = OIDCClient;
// Export singleton instance
exports.oidcClient = new OIDCClient();
//# sourceMappingURL=oidc-client.js.map